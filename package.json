{"name": "react-design-editor", "version": "0.1.0", "private": true, "dependencies": {"@chakra-ui/icons": "^1.0.12", "@chakra-ui/react": "^1.6.0", "@emotion/react": "^11", "@emotion/styled": "^11", "axios": "^0.21.1", "classnames": "^2.3.1", "craco": "^0.0.3", "fabric": "^4.4.0", "focus-visible": "^5.2.0", "framer-motion": "^4", "i18next": "^20.2.2", "lodash": "^4.17.21", "nanoid": "^3.1.22", "node-sass": "^5.0.0", "react": "^17.0.2", "react-color": "^2.19.3", "react-custom-scrollbars": "^4.2.1", "react-dom": "^17.0.2", "react-scripts": "4.0.3", "use-debounce": "^6.0.1", "web-vitals": "^1.0.1"}, "scripts": {"start": "craco start", "build": "craco build", "test": "craco test", "eject": "craco eject", "format": "prettier --write src"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@testing-library/jest-dom": "^5.11.4", "@testing-library/react": "^11.1.0", "@testing-library/user-event": "^12.1.10", "@types/fabric": "^4.2.5", "@types/jest": "^26.0.15", "@types/node": "^12.0.0", "@types/react": "^17.0.0", "@types/react-color": "^3.0.4", "@types/react-dom": "^17.0.0", "prettier": "^2.2.1", "typescript": "^4.1.2"}}